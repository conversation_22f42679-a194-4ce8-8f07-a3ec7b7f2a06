<scroll-view class="container" scroll-y="{{true}}" show-scrollbar="{{false}}" enhanced="{{true}}" enable-back-to-top="{{true}}" bindscroll="onPageScroll" bindscrolltolower="onReachBottom" refresher-enabled="{{true}}" refresher-triggered="{{isRefreshing}}" bindrefresherrefresh="onPullDownRefresh">
  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="padding-top: {{statusBarHeight}}px;">
    <view class="custom-nav-content" style="height: {{navBarHeight}}px;">
      <!-- 位置信息 -->
      <view class="residential-info">
        <!-- <text class="life-text">趣换</text> -->
        <!-- <text class="divider">|</text> -->
        <view class="location-container" bindtap="onSelectLocation" bindlongpress="onLongPressTitle">
          <van-icon name="wap-home-o" size="16px" />
          <text class="residential-name">{{residentialName || '请选择小区'}}</text>
        </view>
        
        <!-- 搜索框 -->
        <view class="search-container">
          <view class="search-input-wrapper">
            <van-icon name="search" size="14px" color="#999" class="search-icon" />
            <input 
              class="search-input" 
              placeholder="搜索商品..." 
              placeholder-class="search-placeholder"
              value="{{searchValue}}" 
              bindinput="onSearchInput" 
              bindconfirm="onSearchConfirm"
              confirm-type="search"
            />
            <view wx:if="{{searchValue}}" class="search-clear" bindtap="onSearchClear">
              <van-icon name="cross" size="12px" color="#999" />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 轮播广告区 -->
  <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}" indicator-color="rgba(0, 0, 0, 0.2)" indicator-active-color="#3B7FFF">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image src="{{item.image}}" class="banner-image" mode="aspectFill" bindtap="onBannerTap" data-id="{{item.id}}"></image>
    </swiper-item>
  </swiper>

  <!-- 功能按钮区域 -->
  <view class="functions-wrapper">
    <!-- 常用电话功能 -->
    <view class="function-item phone-function" bindtap="onPhoneService">
      <view class="function-icon">
        <van-icon name="phone-o" size="20px" color="#ffffff" />
      </view>
      <view class="function-content">
        <view class="function-title">常用电话</view>
        <view class="function-subtitle">便民通讯录</view>
      </view>
      <view class="function-decoration"></view>
    </view>

    <!-- 社区服务功能（周边推荐） -->
    <view class="function-item community-function" bindtap="onCommunityService">
      <view class="function-icon">
        <van-icon name="location-o" size="20px" color="#ffffff" />
      </view>
      <view class="function-content">
        <view class="function-title">周边推荐</view>
        <view class="function-subtitle">便民地图</view>
      </view>
      <view class="function-decoration"></view>
    </view>

    <!-- 邻里互助功能 -->
    <view class="function-item help-function" bindtap="onCommunityHelp">
      <view class="function-icon">
        <van-icon name="friends-o" size="20px" color="#ffffff" />
      </view>
      <view class="function-content">
        <view class="function-title">邻里互助</view>
        <view class="function-subtitle">信息广场</view>
      </view>
      <view class="function-decoration"></view>
    </view>
  </view>

  <!-- 碳豆记录区 -->
  <view class="carbon-bean-area">
    <view class="carbon-bean-header">
      <text class="carbon-title">碳豆排行榜</text>
      <text class="carbon-more" bindtap="onCarbonBeanDetail">详情»</text>
    </view>
    <view class="carbon-bean-list">
      <!-- 有用户数据时循环展示 -->
      <block wx:if="{{carbonUsers && carbonUsers.length > 0}}">
        <!-- 第二名 -->
        <view class="carbon-user rank-second" wx:if="{{carbonUsers.length >= 2}}" bindtap="onCarbonUserTap" data-user-id="{{carbonUsers[1].id}}">
          <view class="user-avatar-container">
            <image src="{{carbonUsers[1].avatar || '/static/img/default_avatar.png'}}" class="user-avatar"></image>
            <view class="user-rank rank-2">
              <text class="rank-number">2</text>
            </view>
          </view>
          <view class="user-name">{{carbonUsers[1].name}}</view>
          <view class="user-reward">{{carbonUsers[1].reward}}</view>
        </view>

        <!-- 第一名 -->
        <view class="carbon-user rank-first" wx:if="{{carbonUsers.length >= 1}}" bindtap="onCarbonUserTap" data-user-id="{{carbonUsers[0].id}}">
          <view class="user-avatar-container">
            <image src="{{carbonUsers[0].avatar || '/static/img/default_avatar.png'}}" class="user-avatar"></image>
            <view class="user-rank rank-1">
              <text class="rank-number">1</text>
            </view>
          </view>
          <view class="user-name">{{carbonUsers[0].name}}</view>
          <view class="user-reward">{{carbonUsers[0].reward}}</view>
        </view>

        <!-- 第三名 -->
        <view class="carbon-user rank-third" wx:if="{{carbonUsers.length >= 3}}" bindtap="onCarbonUserTap" data-user-id="{{carbonUsers[2].id}}">
          <view class="user-avatar-container">
            <image src="{{carbonUsers[2].avatar || '/static/img/default_avatar.png'}}" class="user-avatar"></image>
            <view class="user-rank rank-3">
              <text class="rank-number">3</text>
            </view>
          </view>
          <view class="user-name">{{carbonUsers[2].name}}</view>
          <view class="user-reward">{{carbonUsers[2].reward}}</view>
        </view>
      </block>
      
      <!-- 空状态提示 -->
      <view wx:if="{{!carbonUsers || carbonUsers.length === 0}}" class="carbon-empty-state">
        <van-icon name="medal-o" size="32px" color="#cccccc" />
        <text class="empty-text">暂无排行榜数据</text>
        <text class="empty-subtext">快来获得碳豆吧～</text>
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-tabs">
    <view class="tab {{activeTab === 0 ? 'active' : ''}}" bindtap="onTabChange" data-index="0">
      <text>综合</text>
    </view>
    <view class="tab {{activeTab === 1 ? 'active' : ''}}" bindtap="onTabChange" data-index="1">
      <text>价格</text>
      <van-icon wx:if="{{activeTab === 1 && sortType === 1}}" name="arrow-up" size="12px" />
      <van-icon wx:if="{{activeTab === 1 && sortType === 2}}" name="arrow-down" size="12px" />
    </view>
    <view class="tab {{activeTab === 2 ? 'active' : ''}}" bindtap="onTabChange" data-index="2">
      <text>新发布</text>
      <van-icon wx:if="{{activeTab === 2 && sortType === 3}}" name="arrow-up" size="12px" />
      <van-icon wx:if="{{activeTab === 2 && sortType === 4}}" name="arrow-down" size="12px" />
    </view>
  </view>

  <!-- 商品列表容器 -->
  <view class="swap-items-container">
    <!-- 列表为空时的加载动画 -->
    <view wx:if="{{initialLoading && swapItems.length === 0}}" class="loading-container">
      <!-- 骨架屏 -->
      <view class="skeleton-grid">
        <view class="skeleton-item-column left-column">
          <view class="skeleton-item" wx:for="{{[1,2]}}" wx:key="*this">
            <view class="skeleton-image"></view>
            <view class="skeleton-content">
              <view class="skeleton-line long"></view>
              <view class="skeleton-line short"></view>
              <view class="skeleton-line medium"></view>
            </view>
          </view>
        </view>
        <view class="skeleton-item-column right-column">
          <view class="skeleton-item" wx:for="{{[1,2]}}" wx:key="*this">
            <view class="skeleton-image"></view>
            <view class="skeleton-content">
              <view class="skeleton-line long"></view>
              <view class="skeleton-line short"></view>
              <view class="skeleton-line medium"></view>
            </view>
          </view>
        </view>
      </view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 瀑布流商品列表 -->
    <view class="swap-items" wx:if="{{swapItems.length > 0}}">
      <!-- 左列商品 -->
      <view class="swap-item-column left-column">
        <view class="swap-item" wx:for="{{leftColumnItems}}" wx:key="id" bindtap="onItemDetail" data-id="{{item.id}}">
          <view class="image-container">
            <image src="{{item.firstImage}}" mode="widthFix" lazy-load="{{true}}" class="item-image"></image>
            <view class="residential-badge" wx:if="{{residentialName}}">
              <text class="residential-text">{{residentialName}}</text>
            </view>
          </view>
          <view class="item-info">
            <view class="item-description">{{item.description}}</view>
            <view class="item-meta">
              <view class="item-price">¥{{item.price}}</view>
            </view>
            <!-- 发布时间和用户信息放在同一行 -->
            <view class="item-publisher-row">
              <view class="item-publisher">
                <image class="publisher-avatar" src="{{item.avatar || '/static/img/default_avatar.png'}}"></image>
                <text class="publisher-name">{{item.nickname || '用户名'}}</text>
              </view>
              <view class="item-time">{{item.relativeTime}}</view>
            </view>
          </view>
          <view class="item-tag" wx:if="{{item.isNew}}">新品</view>
        </view>
      </view>

      <!-- 右列商品 -->
      <view class="swap-item-column right-column">
        <view class="swap-item" wx:for="{{rightColumnItems}}" wx:key="id" bindtap="onItemDetail" data-id="{{item.id}}">
          <view class="image-container">
            <image src="{{item.firstImage}}" mode="widthFix" lazy-load="{{true}}" class="item-image"></image>
            <view class="residential-badge" wx:if="{{residentialName}}">
              <text class="residential-text">{{residentialName}}</text>
            </view>
          </view>
          <view class="item-info">
            <view class="item-description">{{item.description}}</view>
            <view class="item-meta">
              <view class="item-price">¥{{item.price}}</view>
            </view>
            <!-- 发布时间和用户信息放在同一行 -->
            <view class="item-publisher-row">
              <view class="item-publisher">
                <image class="publisher-avatar" src="{{item.avatar || '/static/img/default_avatar.png'}}"></image>
                <text class="publisher-name">{{item.nickname || '用户名'}}</text>
              </view>
              <view class="item-time">{{item.relativeTime}}</view>
            </view>
          </view>
          <view class="item-tag" wx:if="{{item.isNew}}">新品</view>
        </view>
      </view>
    </view>

    <!-- 底部加载更多提示 -->
    <view wx:if="{{productLoading && swapItems.length > 0}}" class="bottom-loading">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
    </view>

    <!-- 预加载提示 -->
    <view wx:if="{{isPreloading && swapItems.length > 0}}" class="preload-loading">
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <view class="loading-dot"></view>
      <text class="preload-text">正在加载更多...</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!productLoading && !hasMore && swapItems.length > 0}}" class="no-more-data">
      ——  已经到底了  ——
    </view>

    <!-- 空状态提示 -->
    <view wx:if="{{!initialLoading && swapItems.length === 0}}" class="empty-state">
      <van-icon name="info-o" size="48px" color="#cccccc" />
      <text>暂无数据</text>
    </view>
  </view>

  <!-- 登录组件 -->
  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess"></login-action>

  <!-- 小区认证弹框 -->
  <residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />
</scroll-view>
