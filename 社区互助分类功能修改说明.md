# 社区互助分类功能修改说明

## 修改概述

根据需求，在 systemInfo.js 中添加了 `communityHelpServiceCategories` 配置，并修改了相关页面的分类选择逻辑，实现了以下功能：

1. **分类筛选下拉**：显示需求分类和服务分类的所有数据（去重后）
2. **发布需求时**：分类选择使用 `communityHelpRequireCategories`
3. **发布服务时**：分类选择使用 `communityHelpServiceCategories`

## 具体修改内容

### 1. systemInfo.js 修改

**新增方法：**
- `getCommunityHelpServiceCategories()` - 获取社区互助服务分类列表
- `getFormattedHelpServiceCategories()` - 获取格式化的互助服务分类数据
- `getHelpServiceCategoryNameMap()` - 获取互助服务分类名称映射

**重命名方法：**
- `getFormattedHelpCategories()` → `getFormattedHelpRequireCategories()` (新增，保留旧方法兼容)
- `getHelpCategoryNameMap()` → `getHelpRequireCategoryNameMap()` (新增，保留旧方法兼容)

### 2. 社区互助列表页面 (community-help/index.js)

**修改 `loadCategories()` 方法：**
- 同时获取需求分类和服务分类数据
- 合并两个分类集合并去重（基于 dictValue）
- 在页面数据中存储 `requireCategories` 和 `serviceCategories`

**新增 `updatePublishCategories()` 方法：**
- 根据发布类型（需求/服务）更新可用分类列表
- 在 `onPublishRequest()` 和 `onPublishOffer()` 中调用

### 3. 社区互助发布页面 (community-help-publish/index.js)

**修改 `loadCategories()` 方法：**
- 同时获取需求分类和服务分类数据
- 存储两种分类数据到页面数据中

**新增 `updateCategoriesByPublishType()` 方法：**
- 根据当前发布类型动态设置对应的分类数据
- 发布需求时使用需求分类，提供服务时使用服务分类
- 未选择发布类型时显示所有分类（去重后）

**调用时机：**
- 页面初始化时（新增模式）
- 编辑模式加载详情后
- 分类数据加载完成后

## 数据流程

1. **筛选功能**：
   ```
   requireCategories + serviceCategories → 去重 → 筛选下拉选项
   ```

2. **发布需求**：
   ```
   选择"发布需求" → updatePublishCategories('1') → 显示需求分类
   ```

3. **提供服务**：
   ```
   选择"提供服务" → updatePublishCategories('2') → 显示服务分类
   ```

## 兼容性说明

- 保留了原有的方法名，确保现有代码不受影响
- 新增的方法遵循现有的命名规范和数据格式
- 所有修改都向后兼容，不会影响其他功能

## 兜底策略

为了确保功能的稳定性，在 `communityHelpServiceCategories` 字典数据未配置的情况下：

1. **列表页面**：如果服务分类获取失败或为空，自动使用需求分类作为兜底
2. **发布页面**：如果服务分类获取失败或为空，自动使用需求分类作为兜底
3. **错误处理**：所有分类获取失败时，显示默认的"全部"选项，确保页面正常工作

## 注意事项

1. 需要在后端配置 `communityHelpServiceCategories` 字典数据
2. 两个分类集合的数据格式必须完全一致（dictValue, dictLabel）
3. 去重逻辑基于 `dictValue` 字段，确保相同值的分类不会重复显示
4. 在服务分类数据未配置前，系统会自动使用需求分类作为兜底，确保功能正常
